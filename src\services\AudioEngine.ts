// src/services/AudioEngine.ts
import { getSharedAudioContext } from './AudioManager';
import { RootStoreType } from '../stores/RootStore';
import { DeckStoreInstance } from '../stores/DeckStore';
import { getTrackFileHandle } from './AudioAnalysisService';
import { DeckPath } from './AudioRoutingManager';

/**
 * Interface for audio buffer cache entries
 */
interface AudioBufferCacheEntry {
  buffer: AudioBuffer;
  lastAccessed: number;
}

/**
 * Audio engine for a single deck - now uses the new AudioRoutingManager
 */
export class DeckAudioEngine {
  // Static cache for audio buffers
  private static audioBufferCache = new Map<string, AudioBufferCacheEntry>();
  private static readonly MAX_CACHE_SIZE = 10; // Maximum number of cached buffers

  private audioContext: AudioContext;
  private rootStore: RootStoreType;
  private deckStore: DeckStoreInstance;

  // Audio routing
  private deckPath: DeckPath | null = null;
  private sourceNode: AudioBufferSourceNode | null = null;

  // Audio buffer and playback state
  private audioBuffer: AudioBuffer | null = null;
  private isPlaying: boolean = false;
  private playbackStartTime: number = 0;
  private playbackOffset: number = 0;
  private animationFrameId: number | null = null;

  // Track currently being loaded
  private _loadingTrackPromise: { [trackId: string]: Promise<void> | null } = {};

  constructor(rootStore: RootStoreType, deckStore: DeckStoreInstance) {
    console.log(`DeckAudioEngine: Initializing for deck ${deckStore.id}`);
    this.rootStore = rootStore;
    this.deckStore = deckStore;
    this.audioContext = getSharedAudioContext();
    console.log(`DeckAudioEngine: Got shared audio context, state: ${this.audioContext.state}`);

    // Get the deck path from the audio routing manager
    this.initializeDeckPath();
    // console.log(`DeckAudioEngine: Constructor completed for deck ${deckStore.id}`);
  }

  /**
   * Initialize the deck path from the audio routing manager
   */
  private async initializeDeckPath(): Promise<void> {
    try {
      if(this.deckPath) return;
      console.log(`DeckAudioEngine: Initializing deck path for deck ${this.deckStore.id}`);

      // Add the phase vocoder worklet if not already added
      await this.audioContext.audioWorklet.addModule('/lib/phaze/www/phase-vocoder.js');

      this.deckPath = await this.rootStore.audioRoutingManager.getDeckPath(this.deckStore.id);
      if (!this.deckPath) {
        console.warn(`DeckAudioEngine: No deck path found for deck ${this.deckStore.id}`);
      } else {
        console.log(`DeckAudioEngine: Successfully initialized deck path for deck ${this.deckStore.id}`);
      }
    } catch (error) {
      console.error(`DeckAudioEngine: Failed to get deck path for deck ${this.deckStore.id}:`, error);
    }
  }

  /**
   * Ensure the deck path is available and initialized
   */
  private ensureDeckPath(): boolean {
    console.log(`DeckAudioEngine: Ensuring deck path for deck ${this.deckStore.id}`);
    const result = this.deckPath !== null;
    console.log(`DeckAudioEngine: Deck path check result: ${result}`);
    return result;
  }

  /**
   * Check if audio buffer is loaded
   */
  public isBufferLoaded(): boolean {
    return this.audioBuffer !== null;
  }

  /**
   * Load a track's audio buffer
   */
  public async loadTrack(trackId: string): Promise<void> {
    console.log(`DeckAudioEngine: Load track called for track ${trackId} on deck ${this.deckStore.id}`);
    
    // If this track is already being loaded, return the existing promise
    if (this._loadingTrackPromise[trackId]) {
      console.log(`DeckAudioEngine: Track ${trackId} is already loading for deck ${this.deckStore.id}, returning existing promise`);
      return this._loadingTrackPromise[trackId]!;
    }

    console.log(`DeckAudioEngine: Creating new loading promise for track ${trackId} on deck ${this.deckStore.id}`);
    // Create a new loading promise
    this._loadingTrackPromise[trackId] = this._loadTrackInternal(trackId);

    try {
      await this._loadingTrackPromise[trackId];
      console.log(`DeckAudioEngine: Successfully loaded track ${trackId} on deck ${this.deckStore.id}`);
    } catch (error) {
      console.error(`DeckAudioEngine: Failed to load track ${trackId} on deck ${this.deckStore.id}:`, error);
      throw error;
    } finally {
      // Clean up the promise reference when done
      delete this._loadingTrackPromise[trackId];
      console.log(`DeckAudioEngine: Cleaned up loading promise for track ${trackId} on deck ${this.deckStore.id}`);
    }
  }

  /**
   * Internal method to load a track's audio buffer
   */
  private async _loadTrackInternal(trackId: string): Promise<void> {
    console.log(`DeckAudioEngine: Starting internal load for track ${trackId} on deck ${this.deckStore.id}`);
    
    // Check if we already have this buffer in cache
    if (DeckAudioEngine.audioBufferCache.has(trackId)) {
      console.log(`DeckAudioEngine: Found track ${trackId} in cache for deck ${this.deckStore.id}`);
      const cacheEntry = DeckAudioEngine.audioBufferCache.get(trackId)!;
      this.audioBuffer = cacheEntry.buffer;
      cacheEntry.lastAccessed = Date.now();

      // Set the audio buffer in the deck path
      if (this.ensureDeckPath()) {
        console.log(`DeckAudioEngine: Setting cached audio buffer in deck path for track ${trackId}`);
        this.deckPath!.setAudioBuffer(this.audioBuffer);
      }
      return;
    }

    console.log(`DeckAudioEngine: Loading track ${trackId} from file for deck ${this.deckStore.id}`);
    // Load the track from file
    const fileHandle = await getTrackFileHandle(this.rootStore, trackId);
    if (!fileHandle) {
      console.error(`DeckAudioEngine: No file handle found for track ${trackId} on deck ${this.deckStore.id}`);
      throw new Error(`No file handle found for track ${trackId}`);
    }

    console.log(`DeckAudioEngine: Reading file data for track ${trackId}`);
    const file = await fileHandle.getFile();
    const arrayBuffer = await file.arrayBuffer();

    // Decode audio data
    console.log(`DeckAudioEngine: Decoding audio data for track ${trackId} on deck ${this.deckStore.id}`);
    this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
    console.log(`DeckAudioEngine: Successfully decoded track ${trackId}, duration: ${this.audioBuffer.duration}s`);

    // Cache the buffer
    console.log(`DeckAudioEngine: Caching decoded buffer for track ${trackId}`);
    DeckAudioEngine.audioBufferCache.set(trackId, {
      buffer: this.audioBuffer,
      lastAccessed: Date.now()
    });

    // Set the audio buffer in the deck path
    if (this.ensureDeckPath()) {
      console.log(`DeckAudioEngine: Setting new audio buffer in deck path for track ${trackId}`);
      this.deckPath!.setAudioBuffer(this.audioBuffer);
    }

    // Manage cache size
    this.manageCache();
  }

  /**
   * Manage the size of the audio buffer cache
   */
  private manageCache(): void {
    console.log(`DeckAudioEngine: Managing audio buffer cache. Current size: ${DeckAudioEngine.audioBufferCache.size}`);
    
    if (DeckAudioEngine.audioBufferCache.size <= DeckAudioEngine.MAX_CACHE_SIZE) {
      console.log('DeckAudioEngine: Cache size within limits, no cleanup needed');
      return;
    }

    console.log(`DeckAudioEngine: Cache exceeded size limit of ${DeckAudioEngine.MAX_CACHE_SIZE}, cleaning up oldest entry`);
    // Find the oldest entry
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of DeckAudioEngine.audioBufferCache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    // Remove the oldest entry
    if (oldestKey) {
      DeckAudioEngine.audioBufferCache.delete(oldestKey);
      console.log(`DeckAudioEngine: Removed track ${oldestKey} from cache`);
    }
  }

  /**
   * Play the loaded audio buffer
   */
  public async play(): Promise<void> {
    console.log(`DeckAudioEngine: Play called for deck ${this.deckStore.id}`);
    if (!this.audioBuffer) {
      console.warn(`DeckAudioEngine: Cannot play deck ${this.deckStore.id}: No audio buffer loaded`);
      return;
    }

    if (!this.ensureDeckPath()) {
      console.error(`DeckAudioEngine: No deck path available ${this.deckStore.id}, initializing now`);
      await this.initializeDeckPath();
    }

    if (!this.ensureDeckPath()) {
      console.error(`DeckAudioEngine: Failed to initialize deck path for deck ${this.deckStore.id}, cannot play`);
      return;
    }

    console.log(`DeckAudioEngine: Stopping existing playback for deck ${this.deckStore.id}`);
    // Stop any existing playback
    if (this.sourceNode) {
      this.sourceNode.stop();
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }

    try {
      console.log(`DeckAudioEngine: Creating new source node for deck ${this.deckStore.id}`);
      // Create a new source node
      this.sourceNode = this.deckPath!.createSource();

      // Set up playback rate and master tempo coordination
      const currentRate = this.deckStore.playbackRate;
      this.sourceNode.playbackRate.value = currentRate;

      if (this.deckStore.masterTempo && currentRate !== 1.0) {
        console.log(`DeckAudioEngine: Initializing with Master Tempo ON - source rate 1.0, TimeStretch handles rate ${currentRate}`);
        this.deckPath!.enableMasterTempo(currentRate); // TimeStretchNode handles tempo + pitch correction
      } else {
        console.log(`DeckAudioEngine: Initializing with Master Tempo OFF - source rate ${currentRate}, TimeStretch bypassed`);
        this.deckPath!.disableMasterTempo(); // Bypass TimeStretchNode
      }

      // Start playback
      this.startPlayback();
    } catch (error) {
      console.error(`DeckAudioEngine: Error starting playback for deck ${this.deckStore.id}:`, error);
    }
  }

  /**
   * Helper method to start the actual playback after nodes are connected
   */
  private startPlayback(): void {
    console.log(`DeckAudioEngine: Starting playback for deck ${this.deckStore.id}`);
    // Calculate start time and offset
    this.playbackOffset = this.deckStore.currentTime;
    this.playbackStartTime = this.audioContext.currentTime;

    console.log(`DeckAudioEngine: Starting source node at offset ${this.playbackOffset}s`);
    // Start playback from the current position
    this.sourceNode!.start(0, this.playbackOffset);

    // Update state
    this.isPlaying = true;

    // Start the time update loop
    this.startTimeUpdateLoop();

    console.log(`DeckAudioEngine: Playback started for deck ${this.deckStore.id} at ${this.playbackOffset}s with buffer duration ${this.audioBuffer!.duration}s`);
  }

  /**
   * Pause playback
   */
  public pause(): void {
    console.log(`DeckAudioEngine: Pausing playback at ${this.deckStore.currentTime}s`);
    if (!this.isPlaying || !this.sourceNode) {
      return;
    }

    // Stop the source node
    try {
      this.sourceNode.stop();
    } catch (error) {
      // Source might already be stopped
      console.warn('Error stopping source node:', error);
    }

    this.sourceNode.disconnect();
    this.sourceNode = null;

    // Update state
    this.isPlaying = false;

    // Stop the time update loop
    this.stopTimeUpdateLoop();

    console.log(`DeckAudioEngine: Paused playback at ${this.deckStore.currentTime}s`);
  }

  /**
   * Seek to a specific time
   */
  public seek(timeInSeconds: number): void {
    const wasPlaying = this.isPlaying;

    this.pause();

    // Update the current time
    this.deckStore.setCurrentTime(timeInSeconds);
    this.playbackOffset = timeInSeconds;

    if (wasPlaying) {
      this.play();
    }

    console.log(`DeckAudioEngine: Seeked to ${timeInSeconds}s`);
  }

  /**
   * Set the volume for the deck's main gain node
   */
  public setVolume(volume: number): void {
    console.log(`DeckAudioEngine: Setting volume to ${volume} for deck ${this.deckStore.id}`);
    if (this.ensureDeckPath()) {
      this.deckPath!.setVolume(volume);
      console.log(`DeckAudioEngine: Volume set successfully for deck ${this.deckStore.id}`);
    } else {
      console.warn(`DeckAudioEngine: Could not set volume - no deck path available for deck ${this.deckStore.id}`);
    }
  }

  /**
   * Set the gain for the main crossfader node
   */
  public setCrossfaderGain(gain: number): void {
    console.log(`DeckAudioEngine: Setting crossfader gain to ${gain} for deck ${this.deckStore.id}`);
    if (this.ensureDeckPath()) {
      this.deckPath!.setCrossfaderGain(gain);
      console.log(`DeckAudioEngine: Crossfader gain set successfully for deck ${this.deckStore.id}`);
    } else {
      console.warn(`DeckAudioEngine: Could not set crossfader gain - no deck path available for deck ${this.deckStore.id}`);
    }
  }

  /**
   * Set EQ values for the deck
   */
  public setEQ(
    low: number,
    mid: number,
    high: number,
    midLo?: number,
    midHi?: number
  ): void {
    console.log(`DeckAudioEngine: Setting EQ for deck ${this.deckStore.id}`);
    console.log(`Values - Low: ${low}, Mid: ${mid}, High: ${high}, Mid-Lo: ${midLo}, Mid-Hi: ${midHi}`);

    if (!this.ensureDeckPath()) {
      console.warn(`DeckAudioEngine: Could not set EQ - no deck path available for deck ${this.deckStore.id}`);
      return;
    }

    const equalizerFullKill = this.rootStore.settingsStore.equalizerFullKill;
    const is4BandMode = this.rootStore.settingsStore.eqBands === "4-band";
    console.log(`DeckAudioEngine: EQ Mode - ${is4BandMode ? '4-band' : '3-band'}, Full Kill: ${equalizerFullKill}`);

    if (is4BandMode) {
      this.deckPath!.setEQ({
        low,
        midLo: midLo || 0,
        midHi: midHi || 0,
        high
      }, equalizerFullKill);
    } else {
      this.deckPath!.setEQ({
        low,
        mid,
        high
      }, equalizerFullKill);
    }

    console.log(`DeckAudioEngine: EQ set successfully for deck ${this.deckStore.id}`);
  }

  /**
   * Update EQ frequencies based on settings
   */
  public updateEQFrequencies(): void {
    // The EQ frequencies are handled by the AudioRoutingManager
    // when it creates the EQ nodes, so we don't need to do anything here
    console.log('DeckAudioEngine: EQ frequencies updated by AudioRoutingManager');
  }

  /**
   * Set the playback rate (for pitch/tempo changes)
   */
  public setPlaybackRate(rate: number): void {
    console.log(`DeckAudioEngine: Setting playback rate to ${rate} for deck ${this.deckStore.id}, Master Tempo: ${this.deckStore.masterTempo}`);

    if (!this.ensureDeckPath()) {
      console.warn(`DeckAudioEngine: Could not set playback rate - no deck path available for deck ${this.deckStore.id}`);
      return;
    }
    if (this.sourceNode) {
      this.sourceNode.playbackRate.value = rate;
    }

    if (this.deckStore.masterTempo) {
      // Master Tempo ON: Source node stays at 1.0, TimeStretchNode handles tempo + pitch correction
      console.log(`DeckAudioEngine: Master Tempo ON - setting source rate to 1.0, TimeStretch handles rate ${rate}`);
      this.deckPath!.enableMasterTempo(rate);
    } else {
      // Master Tempo OFF: Source node handles rate directly (tempo + pitch change)
      console.log(`DeckAudioEngine: Master Tempo OFF - setting source rate to ${rate}, TimeStretch bypassed`);
      this.deckPath!.disableMasterTempo();
    }
  }

  /**
   * Enable or disable master tempo
   */
  public setMasterTempo(enabled: boolean): void {
    console.log(`DeckAudioEngine: ${enabled ? 'Enabling' : 'Disabling'} master tempo for deck ${this.deckStore.id}, current rate: ${this.deckStore.playbackRate}`);

    if (!this.ensureDeckPath()) {
      console.warn(`DeckAudioEngine: Could not set master tempo - no deck path available for deck ${this.deckStore.id}`);
      return;
    }

    const currentRate = this.deckStore.playbackRate;

    if (enabled) {
      // Enabling Master Tempo: Transfer rate control from source to TimeStretchNode
      console.log(`DeckAudioEngine: Enabling master tempo - transferring rate control to TimeStretchNode`);
      this.deckPath!.enableMasterTempo(currentRate); // TimeStretchNode handles tempo + pitch correction
    } else {
      // Disabling Master Tempo: Transfer rate control from TimeStretchNode to source
      console.log(`DeckAudioEngine: Disabling master tempo - transferring rate control to source node`);
      this.deckPath!.disableMasterTempo(); // Bypass TimeStretchNode
    }
  }

  /**
   * Start the time update loop for tracking playback position
   */
  private startTimeUpdateLoop(): void {
    console.log(`DeckAudioEngine: Starting time update loop for deck ${this.deckStore.id}`);
    if (this.animationFrameId !== null) {
      console.log(`DeckAudioEngine: Time update loop already running for deck ${this.deckStore.id}`);
      return;
    }

    const updateTime = () => {
      if (!this.isPlaying) {
        console.log(`DeckAudioEngine: Stopping time update loop for deck ${this.deckStore.id} - playback stopped`);
        this.animationFrameId = null;
        return;
      }

      // Calculate current time based on audio context time
      const elapsed = this.audioContext.currentTime - this.playbackStartTime;
      const currentTime = this.playbackOffset + elapsed * this.deckStore.playbackRate;

      // Update the deck store
      this.deckStore.setCurrentTime(currentTime);

      // Check if we've reached the end of the track
      if (this.audioBuffer && currentTime >= this.audioBuffer.duration) {
        console.log(`DeckAudioEngine: Track ended for deck ${this.deckStore.id}`);
        this.pause();
        this.deckStore.setCurrentTime(this.audioBuffer.duration);
        return;
      }

      // Continue the loop
      this.animationFrameId = requestAnimationFrame(updateTime);
    };

    this.animationFrameId = requestAnimationFrame(updateTime);
    console.log(`DeckAudioEngine: Time update loop started for deck ${this.deckStore.id}`);
  }

  /**
   * Stop the time update loop
   */
  private stopTimeUpdateLoop(): void {
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * Get the current playback state
   */
  public getPlaybackState(): {
    isPlaying: boolean;
    currentTime: number;
    duration: number | null;
  } {
    return {
      isPlaying: this.isPlaying,
      currentTime: this.deckStore.currentTime,
      duration: this.audioBuffer ? this.audioBuffer.duration : null
    };
  }

  /**
   * Get the duration of the loaded audio buffer
   */
  public getDuration(): number {
    return this.audioBuffer ? this.audioBuffer.duration : 0;
  }

  /**
   * Get the audio analyzer for this deck
   */
  public getAnalyzer() {
    if (this.ensureDeckPath()) {
      return this.deckPath!.getAnalyzer();
    }
    return null;
  }

  public getSourceNode(): AudioNode | null {
    return this.deckPath ? this.deckPath!.getSourceNode()!.getWebAudioNode() : null;
  }

  /**
   * Dispose of the audio engine and clean up resources
   */
  public dispose(): void {
    console.log(`DeckAudioEngine: Disposing audio engine for deck ${this.deckStore.id}`);

    // Stop playback
    this.pause();

    // Stop time update loop
    this.stopTimeUpdateLoop();

    // Clear references
    this.audioBuffer = null;
    this.deckPath = null;

    console.log(`DeckAudioEngine: Disposed audio engine for deck ${this.deckStore.id}`);
  }
}
