// src/services/AudioRoutingManager.ts

import { getSharedAudioContext } from './AudioManager';
import { RootStoreType } from '../stores/RootStore';

/**
 * Base class for all audio processing nodes in the routing system
 */
export abstract class RoutingAudioNode {
  protected audioContext: AudioContext;
  protected inputNode: RoutingAudioNode | null = null;
  protected outputNodes: Set<RoutingAudioNode> = new Set();
  protected webAudioNode: globalThis.AudioNode | null = null;

  constructor(audioContext: AudioContext) {
    this.audioContext = audioContext;
  }

  /**
   * Connect this node to another audio node
   */
  public connect(destination: RoutingAudioNode): void {
    this.outputNodes.add(destination);
    destination.inputNode = this;
    this.connectWebAudioNodes();
  }

  /**
   * Disconnect this node from another audio node
   */
  public disconnect(destination?: RoutingAudioNode): void {
    if (destination) {
      this.outputNodes.delete(destination);
      destination.inputNode = null;
    } else {
      // Disconnect all outputs
      this.outputNodes.forEach(node => {
        node.inputNode = null;
      });
      this.outputNodes.clear();
    }
    this.disconnectWebAudioNodes();
  }

  /**
   * Get the Web Audio API node for this audio node
   */
  public abstract getWebAudioNode(): globalThis.AudioNode;

  /**
   * Connect the underlying Web Audio API nodes
   */
  protected abstract connectWebAudioNodes(): void;

  /**
   * Disconnect the underlying Web Audio API nodes
   */
  protected abstract disconnectWebAudioNodes(): void;

  /**
   * Dispose of this node and clean up resources
   */
  public dispose(): void {
    this.disconnect();
    this.webAudioNode = null;
  }
}

/**
 * Node that wraps an audio buffer source
 */
export class DeckSourceNode extends RoutingAudioNode {
  private sourceNode: AudioBufferSourceNode | null = null;
  private audioBuffer: AudioBuffer | null = null;

  constructor(audioContext: AudioContext) {
    super(audioContext);
    this.createSource();
  }

  public setAudioBuffer(buffer: AudioBuffer): void {
    this.audioBuffer = buffer;
  }

  public createSource(): AudioBufferSourceNode {
    if (this.sourceNode) {
      this.sourceNode.disconnect();
    }

    this.sourceNode = this.audioContext.createBufferSource();
    this.sourceNode.buffer = this.audioBuffer;
    this.webAudioNode = this.sourceNode;

    // Connect to output nodes if they exist
    this.connectWebAudioNodes();

    return this.sourceNode;
  }

  public getWebAudioNode(): globalThis.AudioNode {
    if (!this.sourceNode) {
      throw new Error('Source node not created. Call createSource() first.');
    }
    return this.sourceNode;
  }

  protected connectWebAudioNodes(): void {
    if (this.sourceNode && this.outputNodes.size > 0) {
      this.outputNodes.forEach(outputNode => {
        this.sourceNode!.connect(outputNode.getWebAudioNode());
      });
    }
  }

  protected disconnectWebAudioNodes(): void {
    if (this.sourceNode) {
      this.sourceNode.disconnect();
    }
  }
}

/**
 * Node that handles time-stretching using Phase Vocoder with wet/dry bypass
 */
export class TimeStretchNode extends RoutingAudioNode {
  private phaseVocoderNode: AudioWorkletNode | null = null;
  private gainCompensationNode: GainNode | null = null;
  private dryGainNode: GainNode | null = null; // Direct bypass path
  private wetGainNode: GainNode | null = null; // Phase vocoder path
  private mixerNode: GainNode | null = null; // Final mixer
  private isBypassed: boolean = true; // Start in bypass mode
  private currentPitchFactor: number = 1.0;

  constructor(audioContext: AudioContext) {
    super(audioContext);

    // Create wet path (phase vocoder)
    this.gainCompensationNode = this.audioContext.createGain();
    this.gainCompensationNode.gain.value = 1;
    this.wetGainNode = this.audioContext.createGain();
    this.wetGainNode.gain.value = 0; // Start with wet path muted

    // Create dry path (direct bypass)
    this.dryGainNode = this.audioContext.createGain();
    this.dryGainNode.gain.value = 1; // Start with dry path active

    // Create final mixer
    this.mixerNode = this.audioContext.createGain();
    this.mixerNode.gain.value = 1;

    try {
      this.phaseVocoderNode = new AudioWorkletNode(this.audioContext, 'phase-vocoder-processor');
      // Initialize with passthrough
      const pitchFactorParam = this.phaseVocoderNode.parameters.get('pitchFactor');
      if (pitchFactorParam) {
        pitchFactorParam.value = 1.0;
      }
    } catch (error) {
      console.error('Failed to initialize Phase Vocoder:', error);
      throw error;
    }
  }

  public setPitchFactor(factor: number): void {
    this.currentPitchFactor = factor;

    if (this.phaseVocoderNode) {
      const pitchFactorParam = this.phaseVocoderNode.parameters.get('pitchFactor');
      if (pitchFactorParam) {
        pitchFactorParam.value = factor;
      }
      if(factor === 1)
        this.gainCompensationNode!.gain.value = 1
      else
        this.gainCompensationNode!.gain.value = 2.69
    }
  }

  /**
   * Enable time stretching (Master Tempo ON) - crossfade to wet path
   */
  public enableTimeStretching(): void {
    console.log('TimeStretchNode: Enabling time stretching - crossfading to wet path');
    this.isBypassed = false;

    // Smoothly crossfade from dry to wet to avoid clicks
    if (this.dryGainNode && this.wetGainNode) {
      const fadeTime = 0.01; // 10ms crossfade
      const currentTime = this.audioContext.currentTime;

      // Fade out dry path
      this.dryGainNode.gain.setValueAtTime(this.dryGainNode.gain.value, currentTime);
      this.dryGainNode.gain.linearRampToValueAtTime(0, currentTime + fadeTime);

      // Fade in wet path
      this.wetGainNode.gain.setValueAtTime(this.wetGainNode.gain.value, currentTime);
      this.wetGainNode.gain.linearRampToValueAtTime(1, currentTime + fadeTime);
    }
  }

  /**
   * Disable time stretching (Master Tempo OFF) - crossfade to dry path
   */
  public disableTimeStretching(): void {
    console.log('TimeStretchNode: Disabling time stretching - crossfading to dry path');
    this.isBypassed = true;

    // Smoothly crossfade from wet to dry to avoid clicks
    if (this.dryGainNode && this.wetGainNode) {
      const fadeTime = 0.01; // 10ms crossfade
      const currentTime = this.audioContext.currentTime;

      // Fade out wet path
      this.wetGainNode.gain.setValueAtTime(this.wetGainNode.gain.value, currentTime);
      this.wetGainNode.gain.linearRampToValueAtTime(0, currentTime + fadeTime);

      // Fade in dry path
      this.dryGainNode.gain.setValueAtTime(this.dryGainNode.gain.value, currentTime);
      this.dryGainNode.gain.linearRampToValueAtTime(1, currentTime + fadeTime);
    }
  }

  public getWebAudioNode(): globalThis.AudioNode {
    if (!this.mixerNode) {
      throw new Error('TimeStretchNode not properly initialized');
    }
    return this.mixerNode;
  }

  protected connectWebAudioNodes(): void {
    if (!this.inputNode) return;

    console.log('TimeStretchNode: Connecting wet/dry paths');

    if (this.phaseVocoderNode && this.gainCompensationNode && this.wetGainNode &&
        this.dryGainNode && this.mixerNode) {

      const inputNode = this.inputNode.getWebAudioNode();

      // Connect wet path: input → phaseVocoder → gainCompensation → wetGain → mixer
      inputNode.connect(this.phaseVocoderNode);
      this.phaseVocoderNode.connect(this.gainCompensationNode);
      this.gainCompensationNode.connect(this.wetGainNode);
      this.wetGainNode.connect(this.mixerNode);

      // Connect dry path: input → dryGain → mixer
      inputNode.connect(this.dryGainNode);
      this.dryGainNode.connect(this.mixerNode);

      // Connect mixer to outputs
      this.outputNodes.forEach(outputNode => {
        this.mixerNode!.connect(outputNode.getWebAudioNode());
      });
    }
  }

  protected disconnectWebAudioNodes(): void {
    // Disconnect all possible connections
    if (this.phaseVocoderNode) {
      this.phaseVocoderNode.disconnect();
    }
    if (this.gainCompensationNode) {
      this.gainCompensationNode.disconnect();
    }
    if (this.wetGainNode) {
      this.wetGainNode.disconnect();
    }
    if (this.dryGainNode) {
      this.dryGainNode.disconnect();
    }
    if (this.mixerNode) {
      this.mixerNode.disconnect();
    }
  }

  public isBypassMode(): boolean {
    return this.isBypassed;
  }

  public getCurrentPitchFactor(): number {
    return this.currentPitchFactor;
  }

  public dispose(): void {
    super.dispose();
    if (this.phaseVocoderNode) {
      this.phaseVocoderNode.disconnect();
      this.phaseVocoderNode = null;
    }
    if (this.gainCompensationNode) {
      this.gainCompensationNode.disconnect();
      this.gainCompensationNode = null;
    }
  }
}

/**
 * Node that handles volume control
 */
export class VolumeNode extends RoutingAudioNode {
  private gainNode: GainNode;

  constructor(audioContext: AudioContext, initialGain: number = 1.0) {
    super(audioContext);
    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = initialGain;
    this.webAudioNode = this.gainNode;
  }

  public setGain(gain: number): void {
    this.gainNode.gain.value = Math.max(0, gain);
  }

  public getGain(): number {
    return this.gainNode.gain.value;
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.gainNode;
  }

  protected connectWebAudioNodes(): void {
    this.outputNodes.forEach(outputNode => {
      this.gainNode.connect(outputNode.getWebAudioNode());
    });
  }

  protected disconnectWebAudioNodes(): void {
    this.gainNode.disconnect();
  }

  public dispose(): void {
    super.dispose();
    this.gainNode.disconnect();
  }
}

/**
 * Node that analyzes audio levels for visualization and auto-master selection
 */
export class AnalyzerNode extends RoutingAudioNode {
  private analyzerNode: AnalyserNode;
  private gainNode: GainNode; // Pass-through node
  private dataArray: Float32Array;
  private rmsValue: number = 0;
  private peakValue: number = 0;
  private isAnalyzing: boolean = false;
  private animationFrameId: number | null = null;

  constructor(audioContext: AudioContext, fftSize: number = 2048) {
    super(audioContext);

    this.analyzerNode = audioContext.createAnalyser();
    this.analyzerNode.fftSize = fftSize;
    this.analyzerNode.smoothingTimeConstant = 0.8;

    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = 1.0;

    // Connect analyzer in parallel with the main signal path
    this.gainNode.connect(this.analyzerNode);

    this.dataArray = new Float32Array(this.analyzerNode.frequencyBinCount);
    this.webAudioNode = this.gainNode;
  }

  public startAnalysis(): void {
    if (this.isAnalyzing) return;

    this.isAnalyzing = true;
    this.analyze();
  }

  public stopAnalysis(): void {
    this.isAnalyzing = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private analyze(): void {
    if (!this.isAnalyzing) return;

    this.analyzerNode.getFloatTimeDomainData(this.dataArray);

    // Calculate RMS and peak values
    let sum = 0;
    let peak = 0;

    for (let i = 0; i < this.dataArray.length; i++) {
      const sample = this.dataArray[i];
      sum += sample * sample;
      peak = Math.max(peak, Math.abs(sample));
    }

    this.rmsValue = Math.sqrt(sum / this.dataArray.length);
    this.peakValue = peak;

    this.animationFrameId = requestAnimationFrame(() => this.analyze());
  }

  public getRMS(): number {
    return this.rmsValue;
  }

  public getPeak(): number {
    return this.peakValue;
  }

  public getRMSdB(): number {
    return this.rmsValue > 0 ? 20 * Math.log10(this.rmsValue) : -Infinity;
  }

  public getPeakdB(): number {
    return this.peakValue > 0 ? 20 * Math.log10(this.peakValue) : -Infinity;
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.gainNode;
  }

  protected connectWebAudioNodes(): void {
    this.outputNodes.forEach(outputNode => {
      this.gainNode.connect(outputNode.getWebAudioNode());
    });
  }

  protected disconnectWebAudioNodes(): void {
    this.gainNode.disconnect();
    // Keep analyzer connected for monitoring
    this.gainNode.connect(this.analyzerNode);
  }

  public dispose(): void {
    super.dispose();
    this.stopAnalysis();
    this.gainNode.disconnect();
    this.analyzerNode.disconnect();
  }
}

/**
 * Node that handles EQ processing
 */
export class EQNode extends RoutingAudioNode {
  private lowEQNode: BiquadFilterNode | null = null;
  private midEQNode: BiquadFilterNode | null = null;
  private midLoEQNode: BiquadFilterNode | null = null;
  private midHiEQNode: BiquadFilterNode | null = null;
  private highEQNode: BiquadFilterNode | null = null;
  private is4BandMode: boolean = false;

  constructor(audioContext: AudioContext, frequencies: {
    low: number;
    mid: number;
    midLo: number;
    midHi: number;
    high: number;
  }) {
    super(audioContext);
    this.setupEQNodes(frequencies);
  }

  private setupEQNodes(frequencies: {
    low: number;
    mid: number;
    midLo: number;
    midHi: number;
    high: number;
  }): void {
    // Create EQ nodes
    this.lowEQNode = this.audioContext.createBiquadFilter();
    this.lowEQNode.type = 'lowshelf';
    this.lowEQNode.frequency.value = frequencies.low;
    this.lowEQNode.Q.value = 1;
    this.lowEQNode.gain.value = 0;

    this.midEQNode = this.audioContext.createBiquadFilter();
    this.midEQNode.type = 'peaking';
    this.midEQNode.frequency.value = frequencies.mid;
    this.midEQNode.Q.value = 1;
    this.midEQNode.gain.value = 0;

    this.midLoEQNode = this.audioContext.createBiquadFilter();
    this.midLoEQNode.type = 'peaking';
    this.midLoEQNode.frequency.value = frequencies.midLo;
    this.midLoEQNode.Q.value = 1;
    this.midLoEQNode.gain.value = 0;

    this.midHiEQNode = this.audioContext.createBiquadFilter();
    this.midHiEQNode.type = 'peaking';
    this.midHiEQNode.frequency.value = frequencies.midHi;
    this.midHiEQNode.Q.value = 1;
    this.midHiEQNode.gain.value = 0;

    this.highEQNode = this.audioContext.createBiquadFilter();
    this.highEQNode.type = 'highshelf';
    this.highEQNode.frequency.value = frequencies.high;
    this.highEQNode.Q.value = 1;
    this.highEQNode.gain.value = 0;

    this.connectEQChain();
  }

  public setMode(is4Band: boolean): void {
    this.is4BandMode = is4Band;
    this.connectEQChain();
  }

  private connectEQChain(): void {
    // Disconnect all nodes first
    this.lowEQNode?.disconnect();
    this.midEQNode?.disconnect();
    this.midLoEQNode?.disconnect();
    this.midHiEQNode?.disconnect();
    this.highEQNode?.disconnect();

    if (this.is4BandMode) {
      // 4-band mode: low → mid-lo → mid-hi → high
      if(this.lowEQNode && this.midLoEQNode && this.midHiEQNode && this.highEQNode) {
      this.lowEQNode.connect(this.midLoEQNode);
      this.midLoEQNode.connect(this.midHiEQNode);
      this.midHiEQNode.connect(this.highEQNode);
      }
    } else {
      // 3-band mode: low → mid → high
      if(this.lowEQNode && this.midEQNode && this.highEQNode) {
        this.lowEQNode.connect(this.midEQNode);
        this.midEQNode.connect(this.highEQNode);
      }
    }
  }

  public setEQ(values: {
    low: number;
    mid?: number;
    midLo?: number;
    midHi?: number;
    high: number;
  }, fullKill: boolean = false): void {
    // Set low EQ
    if (fullKill && values.low < 0) {
      if(this.lowEQNode) {
        this.lowEQNode.gain.value = (values.low / -12) * -50;
      }
    } else {
      if(this.lowEQNode) {
        this.lowEQNode.gain.value = values.low;
      }
    }

    // Set high EQ
    if (fullKill && values.high < 0) {
      if(this.highEQNode) {
        this.highEQNode.gain.value = (values.high / -12) * -50;
      }
    } else {
      if(this.highEQNode) {
        this.highEQNode.gain.value = values.high;
      }
    }

    if (this.is4BandMode) {
      // 4-band mode
      if (values.midLo !== undefined) {
        if (fullKill && values.midLo < 0) {
          if(this.midLoEQNode) {
            this.midLoEQNode.gain.value = (values.midLo / -12) * -50;
          }
        } else {
          if(this.midLoEQNode) {
            this.midLoEQNode.gain.value = values.midLo;
          }
        }
      }

      if (values.midHi !== undefined) {
        if (fullKill && values.midHi < 0) {
          if(this.midHiEQNode) {
            this.midHiEQNode.gain.value = (values.midHi / -12) * -50;
          }
        } else {
          if(this.midHiEQNode) {
            this.midHiEQNode.gain.value = values.midHi;
          }
        }
      }
    } else {
      // 3-band mode
      if (values.mid !== undefined) {
        if (fullKill && values.mid < 0) {
          if(this.midEQNode) {
            this.midEQNode.gain.value = (values.mid / -12) * -50;
          }
        } else {
          if(this.midEQNode) {
            this.midEQNode.gain.value = values.mid;
          }
        }
      }
    }
  }

  public getWebAudioNode(): globalThis.AudioNode {
    if (!this.lowEQNode) {
      throw new Error('EQNode not properly initialized');
    } 
    return this.lowEQNode;
  }

  public getOutputNode(): globalThis.AudioNode {
    if (!this.highEQNode) {
      throw new Error('EQNode not properly initialized');
    }
    return this.highEQNode;
  }

  protected connectWebAudioNodes(): void {
    const outputNode = this.getOutputNode();
    this.outputNodes.forEach(node => {
      outputNode.connect(node.getWebAudioNode());
    });
  }

  protected disconnectWebAudioNodes(): void {
    const outputNode = this.getOutputNode();
    outputNode.disconnect();
  }

  public dispose(): void {
    super.dispose();
    this.lowEQNode?.disconnect();
    this.midEQNode?.disconnect();
    this.midLoEQNode?.disconnect();
    this.midHiEQNode?.disconnect();
    this.highEQNode?.disconnect();
  }
}

/**
 * Node that handles final output routing
 */
export class OutputNode extends RoutingAudioNode {
  private gainNode: GainNode;
  private destination: globalThis.AudioNode | AudioDestinationNode;

  constructor(audioContext: AudioContext, destination?: globalThis.AudioNode | AudioDestinationNode) {
    super(audioContext);
    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = 1.0;
    this.destination = destination || audioContext.destination;
    this.gainNode.connect(this.destination);
    this.webAudioNode = this.gainNode;
  }

  public setDestination(destination: globalThis.AudioNode | AudioDestinationNode): void {
    this.gainNode.disconnect();
    this.destination = destination;
    this.gainNode.connect(this.destination);
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.gainNode;
  }

  public getGainNode(): GainNode {
    return this.gainNode;
  }

  protected connectWebAudioNodes(): void {
    // Output node doesn't connect to other nodes in our system
    // It connects directly to the destination
  }

  protected disconnectWebAudioNodes(): void {
    this.gainNode.disconnect();
    this.gainNode.connect(this.destination);
  }

  public dispose(): void {
    super.dispose();
    this.gainNode.disconnect();
  }
}

/**
 * Represents a complete audio signal path
 */
export abstract class AudioPath {
  protected nodes: RoutingAudioNode[] = [];
  protected audioContext: AudioContext;
  protected isConnected: boolean = false;

  constructor(audioContext: AudioContext) {
    this.audioContext = audioContext;
  }

  /**
   * Connect all nodes in the path
   */
  public connect(): void {
    if (this.isConnected) return;

    for (let i = 0; i < this.nodes.length - 1; i++) {
      this.nodes[i].connect(this.nodes[i + 1]);
    }
    this.isConnected = true;
  }

  /**
   * Disconnect all nodes in the path
   */
  public disconnect(): void {
    if (!this.isConnected) return;

    this.nodes.forEach(node => node.disconnect());
    this.isConnected = false;
  }

  /**
   * Get a specific node by type
   */
  public getNode<T extends RoutingAudioNode>(nodeType: new (...args: any[]) => T): T | null {
    return this.nodes.find(node => node instanceof nodeType) as T || null;
  }

  /**
   * Dispose of all nodes in the path
   */
  public dispose(): void {
    this.disconnect();
    this.nodes.forEach(node => node.dispose());
    this.nodes = [];
  }
}

/**
 * Audio path for a single deck
 */
export class DeckPath extends AudioPath {
  private sourceNode: DeckSourceNode;
  private timeStretchNode: TimeStretchNode;
  private eqNode: EQNode;
  private volumeNode: VolumeNode;
  private crossfaderNode: VolumeNode;
  private analyzerNode: AnalyzerNode;

  constructor(
    audioContext: AudioContext,
    eqFrequencies: {
      low: number;
      mid: number;
      midLo: number;
      midHi: number;
      high: number;
    }
  ) {
    super(audioContext);

    // Create nodes
    this.sourceNode = new DeckSourceNode(audioContext);
    this.timeStretchNode = new TimeStretchNode(audioContext);
    this.eqNode = new EQNode(audioContext, eqFrequencies);
    this.volumeNode = new VolumeNode(audioContext, 0.7);
    this.crossfaderNode = new VolumeNode(audioContext, 1.0);
    this.analyzerNode = new AnalyzerNode(audioContext);

    // Build the path: source → timeStretch → EQ → volume → crossfader → analyzer
    this.nodes = [
      this.sourceNode,
      this.timeStretchNode,
      this.eqNode,
      this.volumeNode,
      this.crossfaderNode,
      this.analyzerNode
    ];
  }

  public enableMasterTempo(playbackRate: number): void {
    console.log(`DeckPath: Enabling master tempo with rate ${playbackRate}`);
    // Enable time stretching and set pitch correction factor
    this.timeStretchNode.enableTimeStretching();
    this.timeStretchNode.setPitchFactor(1.0 / playbackRate);
  }

  public disableMasterTempo(): void {
    console.log('DeckPath: Disabling master tempo');
    // Disable time stretching (bypass mode)
    this.timeStretchNode.disableTimeStretching();
    this.timeStretchNode.setPitchFactor(1.0); // Reset to passthrough
  }

  public getTimeStretchNode(): TimeStretchNode {
    return this.timeStretchNode;
  }

  public setAudioBuffer(buffer: AudioBuffer): void {
    this.sourceNode.setAudioBuffer(buffer);
  }

  public createSource(): AudioBufferSourceNode {
    return this.sourceNode.createSource();
  }

  public setVolume(volume: number): void {
    this.volumeNode.setGain(volume);
  }

  public setCrossfaderGain(gain: number): void {
    this.crossfaderNode.setGain(gain);
  }

  public setEQ(values: {
    low: number;
    mid?: number;
    midLo?: number;
    midHi?: number;
    high: number;
  }, fullKill: boolean = false): void {
    this.eqNode.setEQ(values, fullKill);
  }

  public setEQMode(is4Band: boolean): void {
    this.eqNode.setMode(is4Band);
  }

  public getAnalyzer(): AnalyzerNode {
    return this.analyzerNode;
  }

  public getSourceNode(): DeckSourceNode {
    return this.sourceNode;
  }

  public getOutputNode(): RoutingAudioNode {
    return this.analyzerNode;
  }
}

/**
 * Audio path for master output
 */
export class MasterPath extends AudioPath {
  private inputMixer: VolumeNode;
  private masterVolumeNode: VolumeNode;
  private masterAnalyzer: AnalyzerNode;
  private outputNode: OutputNode;

  constructor(audioContext: AudioContext, destination?: globalThis.AudioNode | AudioDestinationNode) {
    super(audioContext);

    // Create nodes
    this.inputMixer = new VolumeNode(audioContext, 1.0);
    this.masterVolumeNode = new VolumeNode(audioContext, 0.7);
    this.masterAnalyzer = new AnalyzerNode(audioContext);
    this.outputNode = new OutputNode(audioContext, destination);

    // Build path: inputMixer → masterVolume → analyzer → output
    
    this.nodes = [
      this.inputMixer,
      this.masterVolumeNode,
      this.masterAnalyzer,
      this.outputNode
    ];
  }

  public connect(): void {
    console.log('MasterPath: Connecting nodes...');
    if (this.isConnected) {
      console.log('MasterPath: Already connected');
      return;
    }

    try {
      // Connect all nodes in sequence
      for (let i = 0; i < this.nodes.length - 1; i++) {
        this.nodes[i].connect(this.nodes[i + 1]);
        console.log(`MasterPath: Connected node ${i} to node ${i + 1}`);
      }

      // Ensure output node is connected to destination
      const outputNode = this.getOutputNode();
      outputNode.setDestination(this.audioContext.destination);
      console.log('MasterPath: Output node connected to destination');

      this.isConnected = true;
      console.log('MasterPath: All nodes connected successfully');
    } catch (error) {
      console.error('MasterPath: Error connecting nodes:', error);
      throw error;
    }
  }

  public getInputNode(): VolumeNode {
    return this.inputMixer;
  }

  public setMasterVolume(volume: number): void {
    this.masterVolumeNode.setGain(volume);
  }

  public getMasterVolume(): number {
    return this.masterVolumeNode.getGain();
  }

  public getMasterAnalyzer(): AnalyzerNode {
    return this.masterAnalyzer;
  }

  public setOutputDestination(destination: globalThis.AudioNode | AudioDestinationNode): void {
    this.outputNode.setDestination(destination);
  }

  public getNodeCount(): number {
    return this.nodes.length;
  }

  public isPathConnected(): boolean {
    return this.isConnected;
  }

  public getOutputNode(): OutputNode {
    return this.outputNode;
  }
}

/**
 * Audio path for headphone monitoring
 */
export class HeadphonePath extends AudioPath {
  private inputMixer: VolumeNode;
  private headphoneVolumeNode: VolumeNode;
  private outputNode: OutputNode;
  private connectedDecks: Set<string> = new Set();

  constructor(audioContext: AudioContext, destination?: globalThis.AudioNode | AudioDestinationNode) {
    super(audioContext);

    // Create nodes
    this.inputMixer = new VolumeNode(audioContext, 1.0);
    this.headphoneVolumeNode = new VolumeNode(audioContext, 0.5);
    this.outputNode = new OutputNode(audioContext, destination);

    // Build path: inputMixer → headphoneVolume → output
    this.nodes = [
      this.inputMixer,
      this.headphoneVolumeNode,
      this.outputNode
    ];
  }

  public getInputNode(): VolumeNode {
    return this.inputMixer;
  }

  public setHeadphoneVolume(volume: number): void {
    this.headphoneVolumeNode.setGain(volume);
  }

  public addDeck(deckId: string): void {
    this.connectedDecks.add(deckId);
  }

  public removeDeck(deckId: string): void {
    this.connectedDecks.delete(deckId);
  }

  public getConnectedDecks(): Set<string> {
    return new Set(this.connectedDecks);
  }

  public setOutputDestination(destination: globalThis.AudioNode | AudioDestinationNode): void {
    this.outputNode.setDestination(destination);
  }
}

/**
 * Central manager for all audio routing in the DJ application
 */
export class AudioRoutingManager {
  private audioContext: AudioContext;
  private rootStore: RootStoreType;
  private deckPaths: Map<string, DeckPath> = new Map();
  private masterPath: MasterPath;
  private headphonePath: HeadphonePath;
  private isInitialized: boolean = false;

  constructor(rootStore: RootStoreType) {
    this.rootStore = rootStore;
    this.audioContext = getSharedAudioContext();

    // Initialize master and headphone paths
    this.masterPath = new MasterPath(this.audioContext);
    this.headphonePath = new HeadphonePath(this.audioContext);
  }

  /**
   * Initialize the routing manager and create deck paths
   */
  public async initialize(): Promise<void> {
    console.log('AudioRoutingManager: Initializing...');

    if (this.isInitialized) return;

    try {
      // Ensure audio context is running
      if (this.audioContext.state === 'suspended') {
        console.log('AudioRoutingManager: Resuming suspended audio context...');
        await this.audioContext.resume();
        console.log(`AudioRoutingManager: Audio context state: ${this.audioContext.state}`);
      }

      // Ensure master output is properly connected
      this.ensureMasterPathOutput();

      // Connect master path
      this.masterPath.connect();
      console.log('AudioRoutingManager: Master path connected');

      // Connect headphone path
      this.headphonePath.connect();
      console.log('AudioRoutingManager: Headphone path connected');

      // Start analyzers
      this.startAnalyzers();
      console.log('AudioRoutingManager: Analyzers started');

      this.isInitialized = true;
      console.log('AudioRoutingManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AudioRoutingManager:', error);
      throw error;
    }
  }

  /**
   * Create a deck path for a specific deck
   */
  public async createDeckPath(deckId: string): Promise<DeckPath> {
    console.log(`Creating deck path for deck ${deckId}`);

    const eqFrequencies = {
      low: this.rootStore.settingsStore.lowEQFrequency,
      mid: this.rootStore.settingsStore.midEQFrequency,
      midLo: this.rootStore.settingsStore.midLoEQFrequency,
      midHi: this.rootStore.settingsStore.midHiEQFrequency,
      high: this.rootStore.settingsStore.highEQFrequency
    };

    const deckPath = new DeckPath(this.audioContext, eqFrequencies);

    // Set EQ mode based on settings
    deckPath.setEQMode(this.rootStore.settingsStore.eqBands === "4-band");

    // Connect deck path
    deckPath.connect();
    console.log(`AudioRoutingManager: Deck path ${deckId} internal connections made`);

    // Connect deck output to master input
    deckPath.getOutputNode().getWebAudioNode().connect(this.masterPath.getInputNode().getWebAudioNode());
    console.log(`AudioRoutingManager: Deck ${deckId} connected to master input`);

    this.deckPaths.set(deckId, deckPath);

    console.log(`Created deck path for deck ${deckId}`);
    return deckPath;
  }

  /**
   * Get a deck path by deck ID
   */
  public async getDeckPath(deckId: string): Promise<DeckPath | null> {
    console.log(`Getting deck path for deck ${deckId}`, this.deckPaths.has(deckId));
    if (this.deckPaths.has(deckId)) {
      return this.deckPaths.get(deckId) || null;
    } else {
      // when asked for a deck path for a deck we have not seen before create it and return it
      // allows for changing number of decks
      await this.initialize();
      const deckPath = await this.createDeckPath(deckId);
      return deckPath;
    }
  }

  /**
   * Get the master path
   */
  public getMasterPath(): MasterPath {
    return this.masterPath;
  }

  /**
   * Get the headphone path
   */
  public getHeadphonePath(): HeadphonePath {
    return this.headphonePath;
  }

  /**
   * Enable headphone monitoring for a deck
   */
  public enableHeadphoneMonitoring(deckId: string): void {
    const deckPath = this.deckPaths.get(deckId);
    if (!deckPath) {
      console.warn(`Deck path not found for deck ${deckId}`);
      return;
    }

    // Connect deck output to headphone input
    deckPath.getOutputNode().getWebAudioNode().connect(this.headphonePath.getInputNode().getWebAudioNode());
    this.headphonePath.addDeck(deckId);

    console.log(`Enabled headphone monitoring for deck ${deckId}`);
  }

  /**
   * Disable headphone monitoring for a deck
   */
  public disableHeadphoneMonitoring(deckId: string): void {
    const deckPath = this.deckPaths.get(deckId);
    if (!deckPath) {
      console.warn(`Deck path not found for deck ${deckId}`);
      return;
    }

    // Disconnect deck output from headphone input
    deckPath.getOutputNode().getWebAudioNode().disconnect(this.headphonePath.getInputNode().getWebAudioNode());
    this.headphonePath.removeDeck(deckId);

    console.log(`Disabled headphone monitoring for deck ${deckId}`);
  }

  /**
   * Enable master monitoring in headphones
   */
  public enableMasterHeadphoneMonitoring(): void {
    this.masterPath.getMasterAnalyzer().getWebAudioNode().connect(this.headphonePath.getInputNode().getWebAudioNode());
    console.log('Enabled master headphone monitoring');
  }

  /**
   * Disable master monitoring in headphones
   */
  public disableMasterHeadphoneMonitoring(): void {
    this.masterPath.getMasterAnalyzer().getWebAudioNode().disconnect(this.headphonePath.getInputNode().getWebAudioNode());
    console.log('Disabled master headphone monitoring');
  }

  /**
   * Set output devices
   */
  public async setOutputDevice(deviceId: string, type: 'master' | 'headphone'): Promise<void> {
    try {
      // Note: This would require additional Web Audio API support for device selection
      // For now, we'll just log the intent
      console.log(`Setting ${type} output device to: ${deviceId}`);

      // In a full implementation, you would:
      // 1. Create a new AudioContext with the specified device
      // 2. Update the destination nodes accordingly
      // 3. Handle device switching gracefully
    } catch (error) {
      console.error(`Failed to set ${type} output device:`, error);
      throw error;
    }
  }

  /**
   * Fix the master path connection issue by ensuring OutputNode properly connects to destination
   */
  public ensureMasterPathOutput(): void {
    const outputNode = this.masterPath.getOutputNode();
    if (outputNode) {
      // Force reconnect the output node to destination
      outputNode.disconnect();
      outputNode.setDestination(this.audioContext.destination);
      console.log('AudioRoutingManager: Master output node reconnected to destination');
    }
  }

  /**
   * Start all analyzers for level monitoring
   */
  private startAnalyzers(): void {
    // Start deck analyzers
    this.deckPaths.forEach((deckPath, deckId) => {
      const analyzer = deckPath.getAnalyzer();
      analyzer.startAnalysis();

      // Set up level monitoring for auto-master selection
      const updateLevels = () => {
        const rms = analyzer.getRMS();
        this.rootStore.syncStore.updateDeckAudioLevel(deckId, rms);

        if (analyzer.getRMS() > 0) {
          requestAnimationFrame(updateLevels);
        }
      };
      updateLevels();
    });

    // Start master analyzer
    this.masterPath.getMasterAnalyzer().startAnalysis();
  }

  /**
   * Update EQ frequencies for all decks
   */
  public updateEQFrequencies(): void {
    console.log('AudioRoutingManager: Updating EQ frequencies for all decks');
    
    // Note: In a future implementation, we would use these frequencies to update the EQ nodes
    console.log('EQ Frequencies:', {
      low: this.rootStore.settingsStore.lowEQFrequency,
      mid: this.rootStore.settingsStore.midEQFrequency,
      midLo: this.rootStore.settingsStore.midLoEQFrequency,
      midHi: this.rootStore.settingsStore.midHiEQFrequency,
      high: this.rootStore.settingsStore.highEQFrequency
    });

    this.deckPaths.forEach((_path, id) => {
      console.log(`Updating EQ frequencies for deck ${id}`);
      // Future implementation: recreate EQ nodes with new frequencies
    });
  }

  /**
   * Update EQ mode for all decks
   */
  public updateEQMode(): void {
    const is4Band = this.rootStore.settingsStore.eqBands === "4-band";

    this.deckPaths.forEach(deckPath => {
      deckPath.setEQMode(is4Band);
    });

    console.log(`Updated EQ mode to ${is4Band ? '4-band' : '3-band'} for all decks`);
  }

  /**
   * Dispose of the routing manager and clean up all resources
   */
  public dispose(): void {
    console.log('Disposing AudioRoutingManager...');

    // Stop all analyzers
    this.deckPaths.forEach(deckPath => {
      deckPath.getAnalyzer().stopAnalysis();
    });
    this.masterPath.getMasterAnalyzer().stopAnalysis();

    // Dispose of all paths
    this.deckPaths.forEach(deckPath => deckPath.dispose());
    this.deckPaths.clear();

    this.masterPath.dispose();
    this.headphonePath.dispose();

    this.isInitialized = false;
    console.log('AudioRoutingManager disposed');
  }
}